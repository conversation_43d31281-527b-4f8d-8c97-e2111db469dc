<?php

namespace App\Jobs;

use App\Enums\Billing\InvoiceStates;
use App\Models\Billing\Invoice;
use App\Models\Odin\Company;
use App\Models\Odin\ProductAssignment;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class CalculateCompanyRfm implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        // Loop through each company with product_assignment from the last 360 days
        // Calculate the RFM score
        // Update the company record
        $companies = Company::query()
            ->whereHas(Company::RELATION_INVOICES_V2, function ($query) {
                return $query->whereDate(Invoice::FIELD_ISSUE_AT, '>=', now()->subDays(360))->where(Invoice::FIELD_STATUS, '=', InvoiceStates::PAID);
            })->get();

        foreach ($companies as $company) {
            $rfm = $company->invoicesV2->reduce(function ($carry, $invoice) {
                $carry['revenue'] += $invoice->getTotalPaid();
                $carry['frequency'] += 1;
                $carry['monetary'] += $invoice->getTotalPaid();
                return $carry;
            }, ['revenue' => 0, 'frequency' => 0, 'monetary' => 0]);

            $rfm_score = $rfm['revenue'] * $rfm['frequency'] * $rfm['monetary'];
            echo 'Company: ' . $company->name . ' RFM Score: ' . $rfm_score ."\n";
        }
    }

    private function calculateRfm(Company $company)
    {
        $rfm = $company->invoicesV2->reduce(function ($carry, $invoice) {
            $carry['revenue'] += $invoice->total_amount;
            $carry['frequency'] += 1;
            $carry['monetary'] += $invoice->total_amount;
            return $carry;
        }, ['revenue' => 0, 'frequency' => 0, 'monetary' => 0]);

        $rfm_score = $rfm['revenue'] * $rfm['frequency'] * $rfm['monetary'];
        echo 'Company: ' . $company->name . ' RFM Score: ' . $rfm_score;
    }
}

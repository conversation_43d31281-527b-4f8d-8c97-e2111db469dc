<?php

namespace App\DTOs;

class CompanyClassificationMetricsDTO
{
    public function __construct(
        public readonly ?string $rating = null,
        public readonly ?string $reasoning = null,
    ) {}

    /**
     * Create DTO from CompanyMetric request_response array
     */
    public static function fromRequestResponse(?array $requestResponse): self
    {
        if (!$requestResponse) {
            return new self();
        }

        return new self(
            rating: $requestResponse['rating'] ?? null,
            reasoning: $requestResponse['reasoning'] ?? null,
        );
    }

    /**
     * Convert to array
     */
    public function toArray(): array
    {
        return [
            'rating' => $this->rating,
            'reasoning' => $this->reasoning,
        ];
    }
}
